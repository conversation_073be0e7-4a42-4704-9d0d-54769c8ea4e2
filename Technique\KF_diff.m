classdef KF_diff < Kalman2
    % Class: KF_diff - Represent an agent technique that implements Linear KF
    % This class is derived from Kalman2 which is derived from Agent_technique2
    % in order to implement a Linear (Sensor Model) Kalman Filtering technique
    % for multiagents systems. It implements  
    properties
        H
        R
    end

    methods
        function obj = KF_diff(varargin)
            
            obj@Kalman2(varargin);
            
            p = inputParser;
            
            default_H_matrix = eye(obj.y_dim, obj.x_dim);
            check_H_matrix = @(x) isnumeric(x) && all(size(x)==[obj.y_dim, obj.x_dim]);
            addOptional(p, 'H_matrix', default_H_matrix, check_H_matrix);
            
            default_R_matrix = eye(obj.y_dim);
            check_R_matrix = @(x) isnumeric(x) && all(size(x)==[obj.y_dim obj.y_dim]);
            addOptional(p, 'R_matrix', default_R_matrix, check_R_matrix);

            try
                parse(p, varargin{:});

                obj.H = p.Results.H_matrix;
                obj.R = p.Results.R_matrix;

            catch exception
                error('An error occurred: %s', exception.message);
            end
        end

        % Method: update_obs - Update the observation sample in order to process the new sample
        % -------------------------------------
        function y = update_obs(obj, measurement)
            y = zeros(obj.y_dim, 1);
            try
                switch measurement
                    case isa(measurement, 'GeoPoint')
                        y(1) = measurement.get_cart(1).get_m();
                        y(2) = measurement.get_cart(2).get_m();
                    
                    case isa(measurement, 'Displacement')
                        y(1) = measurement.get_cart(1).get_m();
                        y(2) = measurement.get_cart(2).get_m();

                    case isnumeric(measurement) && (length(measurement) == obj.y_dim)
                        y = measurement;
                        y = reshape(y, obj.y_dim, 1);

                    otherwise
                        error('Unknow measurement type.')
                end
            catch exception
                error("An error occurred: %s", exception.message);
            end
        end
        
        % Method: update_y_hat - Based on the prior estimate, update the new expected observation
        % ----------------------------------
        function y_hat = update_y_hat(obj, varargin) 
            y_hat = obj.H * obj.xa_hat; %TODO: Verify
        end
        
        % Method: update_Py - Based on the prior P matrix, update the new Py matrix
        % ---------------------------------
        function Py = update_Py(obj, varargin)
            Py = obj.H * obj.Pa * obj.H';
        end

        % Method: update_Pxy - Based on the prior P matrix, update the new Pxy matrix
        % ---------------------------------
        function Pxy = update_Pxy(obj, varargin)
            Pxy = obj.Pa * obj.H';
        end

        % Method: update_Px - Based on the posterior P matrix, update the new prior P matrix
        % ---------------------------------
        function Px = update_Px(obj, varargin)
            A = obj.update_A(varargin); %TODO: Verify
            % A = update_A@Kalman2(obj, time);
            Px = A * obj.Pp * A';
        end

        % Method: update_Px - Based on the posterior P matrix, update the new prior P matrix
        % ------------------------------------
        function R = update_R_matrix(obj, varargin)
            R = obj.R; 
            R(1,1) = obj.VAR_R; %TODO: Verify
            R(2,2) = obj.VAR_R; %TODO: Verify
        end

        % Method: get_H - get the H matrix to the user.
        % ------------------------------------
        function H = get_H(obj)
            H = obj.H;
        end

        % Method: update_H - Update the new prior P matrix
        % ------------------------------------
        function obj = update_H(obj, H_new)
            obj.H = H_new; %TODO: Verify
        end
    end
end