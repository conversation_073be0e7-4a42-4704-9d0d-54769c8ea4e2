classdef Linear_State < System_Model
    properties (Access = protected)
        Q
        A
    end

    methods
        
        function obj = Linear_State(varargin)

            % Must capture the 'dim' varargin
            obj@System_Model(varargin);
            
            p = inputParser;

            % Defining mandatory arguments             

            % Defining optional arguments
            default_Q_matrix = zeros(obj.dim);
            check_Q_matrix = @(x) isnumeric(x) && any(size(x)==[obj.dim, obj.dim]);
            addOptional(p, 'Q_matrix', default_Q_matrix, check_Q_matrix)

            default_A_matrix = eye(obj.dim);
            check_A_matrix = @(x) isnumeric(x) && any(size(x)==[obj.dim, obj.dim]);
            addOptional(p, 'A_matrix', default_A_matrix, check_A_matrix);
           
            parse(p, varargin{:});
            try
                Q = p.Results.Q_matrix;
                A = p.Results.A_matrix;

                % Check the dimensions
                if ~any(strcmp('Q_matrix', p.UsingDefaults)) && any(size(Q) ~= obj.dim) 
                    error('Q_matrix has wrong number of dimension.')
                elseif ~any(strcmp('A_matrix', p.UsingDefaults)) && any(size(A) ~= obj.dim)
                    error('A_matrix has wrong number of dimension.')
                end
            
                obj.Q = Q;
                obj.A = A;
            catch exception
                error('An error occurred: %s.\n', exception.message)
            end
            
        end

        function Pa = Pa_init(obj, varargin)
            p = inputParser;
            
            default_delta = 1e-1;
            check_delta = @(x) isnumeric(x) && isscalar(x) && (x>0) && (mod(x,1)==0);
            addOptional(p, 'delta', default_delta, check_delta);
            
            % Insert new parameters to init the Pa here

            parse(p, varargin{:});
            try
                Pa = p.Results.delta * eye(obj.dim);
            catch exception
                error('An error occurred: %s.\n', exception.message);
            end    
        end
        
        function xa = xa_init(obj, varargin)
            p = inputParser;

            default_initial_state = zeros(obj.dim, 1);
            check_initial_state = @(x) isnumeric(x) && (length(x)==obj.dim);
            addOptional(p, 'initial_state', default_initial_state, check_initial_state);
            
            parse(p, varargin{:});
            try
                % if ~any(strcmp('initial_state', p.UsingDefaults)) && (length(p.Results.initial_state) ~= obj.dim)
                %     error('The initial state used is different than model dimension.')
                % end
                xa = p.Results.initial_state;
            catch exception
                error('An error occurred: %s.\n', exception.message);
            end
        end

        function Q = update_Q(obj, varargin)
            Q = obj.Q;
        end
        
        function A = update_A(obj, varargin)
            A = obj.A;
        end
    end
end